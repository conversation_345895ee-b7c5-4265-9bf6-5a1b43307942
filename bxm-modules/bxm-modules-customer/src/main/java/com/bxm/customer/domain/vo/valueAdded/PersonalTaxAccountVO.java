package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 个税账号VO
 *
 * 用于展示个税账号的详细信息，字段名称与业务表单保持一致
 * 继承BaseTaxAccountVO，包含个税账号特有的业务逻辑
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("个税账号VO")
public class PersonalTaxAccountVO extends BaseTaxAccountVO {

    /** 操作方式：1-个税账号添加 */
    @ApiModelProperty(value = "操作方式：1-个税账号添加")
    private Integer operationType;
}
