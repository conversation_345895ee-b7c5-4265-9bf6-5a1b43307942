package com.bxm.customer.domain.constants;

/**
 * 增值服务相关常量
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public class ValueAddedConstants {

    /** 增值事项类型常量 */
    public static final class ItemCode {
        /** 社医保 */
        public static final String TAX_SOCIAL_INSURANCE = "TAX_SOCIAL_INSURANCE";

        /** 个税明细 */
        public static final String TAX_PERSONAL_SALARY = "TAX_PERSONAL_SALARY";

        /** 个税其他 */
        public static final String TAX_PERSONAL_OTHER = "TAX_PERSONAL_OTHER";
    }

    /** 业务类型常量 */
    public static final class BizType {
        /** 社医保 */
        public static final Integer SOCIAL_INSURANCE = 1;
        
        /** 个税明细 */
        public static final Integer PERSONAL_TAX = 2;
        
        /** 国税账号 */
        public static final Integer NATIONAL_TAX_ACCOUNT = 3;
        
        /** 个税账号 */
        public static final Integer PERSONAL_TAX_ACCOUNT = 4;
    }

    /** 文件类型常量 */
    public static final class FileType {
        /** 交付材料附件 */
        public static final Integer DELIVERY_MATERIAL = 1;
        
        /** 人员明细excel */
        public static final Integer EMPLOYEE_DETAIL_EXCEL = 2;
    }

    /** 员工查询限制 */
    public static final class EmployeeLimit {
        /** 最大查询数量 */
        public static final Integer MAX_QUERY_SIZE = 50;
    }

    /** 增值事项类型ID常量 */
    public static final class ItemTypeId {
        /** 社医保对应的增值事项类型ID */
        public static final Integer SOCIAL_INSURANCE = 1;
        
        /** 个税明细对应的增值事项类型ID */
        public static final Integer PERSONAL_TAX = 2;
    }
}
