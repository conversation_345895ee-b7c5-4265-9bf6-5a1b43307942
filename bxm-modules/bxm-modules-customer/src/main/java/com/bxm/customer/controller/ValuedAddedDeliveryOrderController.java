package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderUpsertReq;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.domain.vo.valueAdded.SaveStatusReqVO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationResultDTO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.IBatchValueAddedOperationService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.common.security.utils.DictUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import com.bxm.system.api.domain.SysDictData;
import com.bxm.common.core.web.domain.TextVO;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private IBatchValueAddedOperationService batchOperationService;


    /**
     * 新增或更新增值交付单
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值交付单", notes = "支持新增和更新操作，根据ID或交付单编号自动判断")
    @Log(title = "Upsert value added delivery order", businessType = BusinessType.INSERT)
    public Result<DeliveryOrderUpsertReq> upsert(@Valid @RequestBody DeliveryOrderUpsertReq orderVO) {
        try {
            log.info("Upsert delivery order request: {}", orderVO.getCustomerName());
            // 调用服务层进行upsert操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.upsert(orderVO);
            // 转换为VO返回
            DeliveryOrderUpsertReq resultVO = new DeliveryOrderUpsertReq();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Upsert delivery order success: {}", result.getDeliveryOrderNo());
            return Result.ok(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Upsert delivery order validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Upsert delivery order failed for customer: {}", orderVO.getCustomerName(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 条件查询增值交付单
     * 所有条件在 service 层进行动态拼接
     */
    @GetMapping("/query")
    @ApiOperation(value = "增值交付单查询", notes = "按条件分页查询增值交付单；条件均为可选")
    @Log(title = "Query value added delivery order", businessType = BusinessType.OTHER)
    public Result<IPage<DeliveryOrderVO>> query(DeliveryOrderQuery query) {
        try {

            // 创建分页对象
            IPage<DeliveryOrderVO> page = new Page<>(
                query.getPageNum() != null ? query.getPageNum() : 1,
                query.getPageSize() != null ? query.getPageSize() : 10
            );
            // 调用Service层进行分页查询
            IPage<DeliveryOrderVO> result = valueAddedDeliveryOrderService.queryVOPage(page, query);

            log.info("Query delivery orders success, total: {}, current page: {}",
                result.getTotal(), result.getCurrent());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("Query delivery orders failed", e);
            return Result.fail("查询增值交付单失败: " + e.getMessage());
        }
    }

    /**
     * 根据交付单编号查询增值交付单详细信息
     *
     * @param deliveryOrderNo 交付单编号
     * @return 查询结果，包含基本信息和关联的扩展数据
     */
    @GetMapping("/getDeliveryOrder/{deliveryOrderNo}")
    @ApiOperation(value = "根据交付单编号查询详细信息", notes = "根据交付单编号查询增值交付单详情，包含国税账号、个税账号、员工信息列表、交付文件等扩展数据")
    @Log(title = "Get delivery order details by order number", businessType = BusinessType.OTHER)
    public Result<DeliveryOrderVO> getDeliveryOrder(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query delivery order details by order number: {}", deliveryOrderNo);
            DeliveryOrderVO orderVO = valueAddedDeliveryOrderService.getDeliveryOrderVO(deliveryOrderNo);
            if (orderVO == null) {
                return Result.fail("未找到对应的增值交付单");
            }
            log.info("Query delivery order details success: {}", deliveryOrderNo);
            return Result.ok(orderVO);
        } catch (Exception e) {
            log.error("Failed to query delivery order details by order number: {}", deliveryOrderNo, e);
            return Result.fail("查询增值交付单详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成交付单编号
     * 编号规则：VAD + yyMMddHHmmsss + 3位随机码，总长度19位
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳到毫秒+随机码，总长度19位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public Result<String> genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmmssSSS格式（13位）
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));
            // 生成3位随机码
            String randomCode = StringUtils.generateRandomCode(2);
            // 组合生成最终编号
            String deliveryOrderNo = "SW" + timestamp + randomCode;
            log.info("Generated delivery order number: {}", deliveryOrderNo);
            return Result.ok(deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return Result.fail("生成交付单编号失败");
        }
    }

    /**
     * 查询增值事项类型列表
     *
     * @return 增值事项类型列表
     */
    @GetMapping("/listItemType")
    @ApiOperation(value = "查询增值事项类型列表", notes = "获取所有可用的增值事项类型")
    @Log(title = "List value added item types", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedItemTypeVO>> listItemType() {
        try {
            log.info("Query value added item type list");
            List<ValueAddedItemTypeVO> itemTypes = valueAddedItemTypeService.listItemTypeVO();
            log.info("Query value added item type list success, count: {}", itemTypes.size());
            return Result.ok(itemTypes);
        } catch (Exception e) {
            log.error("Query value added item type list failed", e);
            return Result.fail("查询增值事项类型列表失败");
        }
    }

    /**
     * 修改增值交付单状态
     *
     * @param request 状态变更请求
     * @return 操作结果
     */
    @PostMapping("/changeStatus")
    @ApiOperation(value = "修改增值交付单状态", notes = "通过状态机管理器修改交付单状态，包含完整的业务验证")
    @Log(title = "Change delivery order status", businessType = BusinessType.UPDATE)
    public Result<String> changeStatus(@Valid @RequestBody StatusChangeRequestDTO request) {
        try {
            log.info("Change status request for order: {} to status: {}", request.getDeliveryOrderNo(), request.getTargetStatus());

            // 调用服务层进行状态变更
            valueAddedDeliveryOrderService.changeStatus(request);

            log.info("Change status success for order: {} to status: {}", request.getDeliveryOrderNo(), request.getTargetStatus());
            return Result.ok("状态修改成功");
        } catch (IllegalArgumentException e) {
            log.warn("Change status validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Change status failed for order: {}", request != null ? request.getDeliveryOrderNo() : "unknown", e);
            return Result.fail("状态修改失败");
        }
    }

    /**
     * 获取指定交付单的可用状态列表
     *
     * @param deliveryOrderNo 交付单编号
     * @return 可用状态列表
     */
    @GetMapping("/availableStatuses/{deliveryOrderNo}")
    @ApiOperation(value = "获取可用状态列表", notes = "根据当前状态获取所有可以转换到的目标状态")
    @Log(title = "Get available statuses", businessType = BusinessType.OTHER)
    public Result<List<ValueAddedDeliveryOrderStatus>> getAvailableStatuses(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Get available statuses for order: {}", deliveryOrderNo);

            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    valueAddedDeliveryOrderService.getAvailableStatuses(deliveryOrderNo);

            log.info("Get available statuses success for order: {}, count: {}", deliveryOrderNo, availableStatuses.size());
            return Result.ok(availableStatuses);

        } catch (IllegalArgumentException e) {
            log.warn("Get available statuses validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Get available statuses failed for order: {}", deliveryOrderNo, e);
            return Result.fail("获取可用状态列表失败");
        }
    }

    /**
     * 保存状态信息
     *
     * @param request 保存状态请求
     * @return 操作结果
     */
    @PostMapping("/saveStatus")
    @ApiOperation(value = "保存状态信息", notes = "用于待交付、待扣款状态的保存操作，将目标状态存储到processing_status字段")
    @Log(title = "Save delivery order status", businessType = BusinessType.UPDATE)
    public Result<String> saveStatus(@Valid @RequestBody SaveStatusReqVO request) {
        try {
            log.info("Save status request for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 调用服务层进行状态保存
            valueAddedDeliveryOrderService.saveStatus(request);

            log.info("Save status success for order: {} to status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());
            return Result.ok("状态保存成功");
        } catch (IllegalArgumentException e) {
            log.warn("Save status validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Save status failed for order: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e);
            return Result.fail("状态保存失败");
        }
    }

    /**
     * 批量操作增值交付单
     *
     * @param request 批量操作请求
     * @return 操作结果
     */
    @PostMapping("/batchOperation")
    @ApiOperation(value = "批量操作增值交付单", notes = "支持批量确认、提交、关闭、处理异常、驳回、退回等操作")
    @Log(title = "Batch operation delivery orders", businessType = BusinessType.UPDATE)
    public Result<BatchOperationResultDTO> batchOperation(@Valid @RequestBody BatchOperationRequestDTO request) {
        try {
            log.info("Batch operation request: {}, order count: {}",
                    request.getOperationType().getDescription(), request.getOrderCount());

            // 执行批量操作
            BatchOperationResultDTO result = batchOperationService.executeBatchOperation(request);

            log.info("Batch operation completed: {}, total: {}, success: {}, error: {}",
                    request.getOperationType().getDescription(),
                    result.getTotalCount(), result.getSuccessCount(), result.getErrorCount());

            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch operation validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch operation failed: {}", e.getMessage(), e);
            return Result.fail("批量操作失败");
        }
    }

    /**
     * 导出批量操作异常数据
     *
     * @param batchNo 批次号
     * @param response HTTP响应
     */
    @GetMapping("/exportBatchErrors/{batchNo}")
    @ApiOperation(value = "导出批量操作异常数据", notes = "根据批次号导出批量操作过程中产生的异常数据")
    @Log(title = "Export batch operation errors", businessType = BusinessType.EXPORT)
    public void exportBatchErrors(@PathVariable String batchNo, HttpServletResponse response) {
        try {
            log.info("Export batch operation errors for batchNo: {}", batchNo);

            // 获取异常数据
            List<BatchOperationErrorDTO> errorList = batchOperationService.getBatchOperationErrors(batchNo);

            if (errorList.isEmpty()) {
                throw new RuntimeException("未找到异常数据或数据已过期");
            }

            // 导出Excel
            ExcelUtil<BatchOperationErrorDTO> util = new ExcelUtil<>(BatchOperationErrorDTO.class);
            util.exportExcel(response, errorList, "批量操作异常数据");

            log.info("Export batch operation errors completed for batchNo: {}, count: {}", batchNo, errorList.size());
        } catch (Exception e) {
            log.error("Export batch operation errors failed for batchNo: {}", batchNo, e);
            throw new RuntimeException("导出异常数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除增值交付单
     *
     * @param deliveryOrderNos 交付单编号列表
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param reason 删除原因
     * @return 操作结果
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除增值交付单", notes = "批量删除符合条件的交付单，删除条件：必须为已关闭交付、已扣款、已关闭扣款状态")
    @Log(title = "Batch delete delivery orders", businessType = BusinessType.DELETE)
    public Result<BatchOperationResultDTO> batchDelete(
            @RequestParam("deliveryOrderNos") List<String> deliveryOrderNos,
            @RequestParam("operatorId") Long operatorId,
            @RequestParam(value = "operatorName", required = false) String operatorName,
            @RequestParam(value = "reason", required = false) String reason) {
        try {
            log.info("Batch delete request: order count: {}, operator: {}",
                    deliveryOrderNos.size(), operatorName);

            // 执行批量删除
            BatchOperationResultDTO result = batchOperationService.executeBatchDelete(
                    deliveryOrderNos, operatorId, operatorName, reason);

            log.info("Batch delete completed: total: {}, success: {}, error: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getErrorCount());

            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Batch delete failed: {}", e.getMessage(), e);
            return Result.fail("批量删除失败");
        }
    }

    /**
     * 获取账户子类型
     *
     * @param accountType 账务主类型，可选参数，默认为NON_STANDARD
     * @return 账户子类型列表，格式为TextVO数组，key为英文，value为中文
     */
    @GetMapping("/getAccountSubType")
    @ApiOperation(value = "获取账户子类型", notes = "根据账务主类型获取对应的账户子类型列表，返回key-value格式的TextVO数组")
    @Log(title = "Get account sub types", businessType = BusinessType.OTHER)
    public Result<List<TextVO>> getAccountSubType(@RequestParam(value = "accountType", required = false, defaultValue = "NON_STANDARD") String accountType) {
        try {
            log.info("Query account sub types from dictionary for type: {}", accountType);

            // 从字典缓存获取指定类型的数据
            List<SysDictData> dictDataList = DictUtils.getDictCache(accountType);

            if (dictDataList == null || dictDataList.isEmpty()) {
                log.warn("No account sub type data found in dictionary for type: {}", accountType);
                return Result.ok(new ArrayList<>());
            }

            // 转换为用户要求的格式：key为英文（dictValue），value为中文（dictLabel）
            List<TextVO> result = dictDataList.stream()
                    .map(dictData -> TextVO.of(dictData.getDictValue(), dictData.getDictLabel()))
                    .collect(Collectors.toList());

            log.info("Query account sub types success for type: {}, count: {}", accountType, result.size());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("Query account sub types failed for type: {}", accountType, e);
            return Result.fail("获取账户子类型失败");
        }
    }

}
