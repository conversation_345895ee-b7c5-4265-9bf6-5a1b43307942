package com.bxm.common.core.web.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用文本键值对VO
 * 
 * 用于返回key-value格式的数据，如字典数据、枚举数据等
 * 
 * <AUTHOR>
 * @date 2025-08-23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("通用文本键值对VO")
public class TextVO {

    @ApiModelProperty(value = "键值（英文标识）", required = true)
    private String key;

    @ApiModelProperty(value = "显示值（中文描述）", required = true)
    private String value;

    /**
     * 创建TextVO实例的便捷方法
     * 
     * @param key 键值
     * @param value 显示值
     * @return TextVO实例
     */
    public static TextVO of(String key, String value) {
        return TextVO.builder()
                .key(key)
                .value(value)
                .build();
    }
}
