-- ===================================================================
-- 账号子类型字典数据初始化脚本
-- 功能：创建NON_STANDARD字典类型，包含高新账、凭票入账、特殊行业、民非等子类型
-- 作者：system
-- 创建时间：2025-01-27
-- ===================================================================

-- 1. 创建字典类型：NON_STANDARD（账号子类型）
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('账号子类型', 'NON_STANDARD', '0', 'admin', NOW(), '账号子类型字典，用于区分不同类型的非标准账号');

-- 2. 创建字典数据项
-- 高新账
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '高新账', 'HIGH_TECH', 'NON_STANDARD', 'success', '', 'N', '0', 'admin', NOW(), '高新技术企业账号类型');

-- 凭票入账
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '凭票入账', 'VOUCHER_BASED', 'NON_STANDARD', 'info', '', 'N', '0', 'admin', NOW(), '凭票入账类型账号');

-- 特殊行业
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '特殊行业', 'SPECIAL_INDUSTRY', 'NON_STANDARD', 'warning', '', 'N', '0', 'admin', NOW(), '特殊行业账号类型');

-- 民非
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (4, '民非', 'NON_PROFIT', 'NON_STANDARD', 'primary', '', 'N', '0', 'admin', NOW(), '民办非企业单位账号类型');

